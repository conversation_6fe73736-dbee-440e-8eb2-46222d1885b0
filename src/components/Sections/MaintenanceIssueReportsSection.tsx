import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Card, Section } from "../main";

import { theme } from "../../theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { PartialProperty } from "../../interfaces/property";
import { MaintenanceIssueReport } from "../../interfaces/maintenance-issue-report";
import { formatDateDMY } from "../../utils/date-time.utils";
import {
  getCategoryColor,
  getStatusColor,
  getStatusIcon,
  getStatusLabel,
} from "../../utils/convertions";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { NoDataSection } from "./NoDataSection";

interface MaintenanceIssueReportsSectionProps {
  maintenanceIssueReports: MaintenanceIssueReport[];
  property: PartialProperty;
}

export const MaintenanceIssueReportsSection: React.FC<
  MaintenanceIssueReportsSectionProps
> = ({ maintenanceIssueReports, property }) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!maintenanceIssueReports.length) {
    return (
      <NoDataSection
        sectionTitle="Reportes de mantenimiento"
        text="No hay reportes de mantenimiento en proceso"
      />
    );
  }

  return (
    <Section title="Reportes de mantenimiento">
      <>
        {maintenanceIssueReports.map((report) => (
          <TouchableOpacity
            key={report.id}
            onPress={() =>
              navigation.navigate(
                PROPERTY_SCREENS.MAINTENANCE_ISSUE_REPORT_DETAIL,
                { report }
              )
            }
            activeOpacity={0.7}
          >
            <Card
              style={[
                styles.reportCard,
                { borderLeftColor: getCategoryColor(report.description) },
              ]}
            >
              <Row align="flex-start">
                <Col style={styles.reportInfo}>
                  <Row align="center" style={styles.headerRow}>
                    <Text style={styles.reportDescription} numberOfLines={2}>
                      {report.description}
                    </Text>
                    <View
                      style={[
                        styles.statusChip,
                        { backgroundColor: getStatusColor(report.status) },
                      ]}
                    >
                      <MaterialCommunityIcons
                        name={getStatusIcon(report.status)}
                        size={12}
                        color={theme.colors.white}
                      />
                      <Text style={styles.statusText}>
                        {getStatusLabel(report.status)}
                      </Text>
                    </View>
                  </Row>
                  <Row align="center" style={styles.dateRow}>
                    <MaterialCommunityIcons
                      name="calendar"
                      size={14}
                      color={theme.colors.gray500}
                    />
                    <Text style={styles.dateText}>
                      Creado: {formatDateDMY(report.createdAt)}
                    </Text>
                  </Row>
                </Col>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={theme.fontSizes.lg}
                  color={theme.colors.gray500}
                />
              </Row>
            </Card>
          </TouchableOpacity>
        ))}
      </>
    </Section>
  );
};

const styles = StyleSheet.create({
  reportCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
    borderRadius: theme.radii.md,
    borderLeftWidth: 4,
  },
  categoryIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  reportInfo: {
    flex: 1,
    paddingLeft: theme.spacing.md,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
    alignItems: "flex-start",
  },
  reportDescription: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
    lineHeight: 20,
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  dateRow: {
    marginTop: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
});
