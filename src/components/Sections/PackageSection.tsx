import { StyleSheet, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Package } from "../../interfaces/package";
import { Card, Section } from "../main";
import { theme } from "../../theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { Property } from "../../interfaces/property";
import { Row } from "../main/Row";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { NoDataSection } from "./NoDataSection";

interface PackageSectionProps {
  packages: Package[];
  propertyId: Property["id"];
}

export const PackageSection: React.FC<PackageSectionProps> = ({
  packages,
  propertyId,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!packages.length) {
    return (
      <NoDataSection
        sectionTitle="Paquetes"
        text="No hay paquetes registrados"
      />
    );
  }

  return (
    <Section title="Paquetes">
      <>
        {/* Mostrar solo los primeros 3 paquetes pendientes */}
        {packages.slice(0, 3).map((pkg) => (
          <TouchableOpacity
            key={pkg.id}
            onPress={() =>
              navigation.navigate(PROPERTY_SCREENS.PACKAGE_DETAIL, {
                package: pkg,
              })
            }
          >
            <Card style={styles.packageCard}>
              <Row style={styles.headerRow}>
                <View style={styles.packageIcon}>
                  <MaterialCommunityIcons
                    name="package-variant"
                    size={24}
                    color={theme.colors.primary}
                  />
                </View>
              </Row>
            </Card>
          </TouchableOpacity>
        ))}
      </>
    </Section>
  );
};

const styles = StyleSheet.create({
  packageCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  headerRow: {
    alignItems: "flex-start",
  },
  packageIcon: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    backgroundColor: theme.colors.primary + "15",
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },

  viewAllCard: {
    marginTop: theme.spacing.sm,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.primary + "08",
    borderWidth: 1,
    borderColor: theme.colors.primary + "20",
  },
  viewAllRow: {
    alignItems: "center",
  },
  viewAllIcon: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    backgroundColor: theme.colors.primary + "15",
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  viewAllInfo: {
    flex: 1,
  },
  viewAllTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: theme.colors.primary,
    marginBottom: 2,
  },
  viewAllSubtitle: {
    fontSize: 14,
    color: theme.colors.black,
  },
});
