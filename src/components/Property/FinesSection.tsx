import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { theme } from "../../theme";
import { Section } from "../main/Section";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { Property } from "../../interfaces/property";
import { PROPERTY_SCREENS } from "../../navigation";
import { Card, Row } from "../main";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Col } from "../main/Col";
import { formatDateDMY } from "../../utils/date-time.utils";
import { formatCurrency } from "./MonthlyChargesSection";
import { Fine } from "../../interfaces/fine";
import { NoDataSection } from "../Sections/NoDataSection";

interface FinesSectionProps {
  fines: Fine[];
  propertyId: Property["id"];
}

export const FinesSection: React.FC<FinesSectionProps> = ({
  fines,
  propertyId,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!fines.length) {
    return <NoDataSection sectionTitle="Multas" text="No hay multas nuevas" />;
  }

  return (
    <Section title="Multas">
      <>
        {fines.map((fine) => (
          <TouchableOpacity
            key={fine.id}
            onPress={() =>
              navigation.navigate(PROPERTY_SCREENS.FINE_DETAIL, { fine })
            }
            activeOpacity={0.7}
          >
            <Card
              style={[
                styles.fineCard,
                {
                  borderLeftColor: fine.isPaid
                    ? theme.colors.success
                    : theme.colors.error,
                },
              ]}
            >
              <Row align="flex-start">
                <Col style={styles.fineInfo}>
                  <Row align="center" style={styles.headerRow}>
                    <Text style={styles.fineAmount}>
                      {formatCurrency(fine.amount)}
                    </Text>
                    <View
                      style={[
                        styles.statusChip,
                        {
                          backgroundColor: fine.isPaid
                            ? theme.colors.success
                            : theme.colors.error,
                        },
                      ]}
                    >
                      <MaterialCommunityIcons
                        name={fine.isPaid ? "check" : "close"}
                        size={12}
                        color={theme.colors.white}
                      />
                      <Text style={styles.statusText}>
                        {fine.isPaid ? "Pagada" : "Pendiente"}
                      </Text>
                    </View>
                  </Row>
                  <Text style={styles.fineDescription} numberOfLines={3}>
                    {fine.description}
                  </Text>
                  <Row align="center" style={styles.dateRow}>
                    <MaterialCommunityIcons
                      name="calendar"
                      size={14}
                      color={theme.colors.gray500}
                    />
                    <Text style={styles.dateText}>
                      Emitida: {formatDateDMY(fine.issuedAt)}
                    </Text>
                    {!!fine.paidAt && (
                      <>
                        <MaterialCommunityIcons
                          name="check"
                          size={14}
                          color={theme.colors.success}
                          style={styles.paidIcon}
                        />
                        <Text style={styles.paidText}>
                          Pagada: {formatDateDMY(fine.paidAt)}
                        </Text>
                      </>
                    )}
                  </Row>
                </Col>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={theme.fontSizes.lg}
                  color={theme.colors.gray500}
                />
              </Row>
            </Card>
          </TouchableOpacity>
        ))}
      </>
    </Section>
  );
};

const styles = StyleSheet.create({
  fineCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
    borderRadius: theme.radii.md,
    borderLeftWidth: 4,
  },
  statusIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  fineInfo: {
    flex: 1,
    paddingLeft: theme.spacing.md,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  fineAmount: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  fineDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.black,
    lineHeight: 18,
    marginBottom: theme.spacing.sm,
  },
  dateRow: {
    marginBottom: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  paidIcon: {
    marginLeft: theme.spacing.md,
  },
  paidText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.success,
    marginLeft: 6,
  },
});
