import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { theme } from "../../theme";
import { Infraction } from "../../interfaces/infraction";
import { Card } from "../main/cards/Card";
import { Section } from "../main/Section";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { Property } from "../../interfaces/property";
import { Row } from "../main";
import { Col } from "../main/Col";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { formatDateDMY } from "../../utils/date-time.utils";
import { getSeverityData } from "../../utils/convertions";
import { NoDataSection } from "../Sections/NoDataSection";

interface InfractionsSectionProps {
  infractions: Infraction[];
  propertyId: Property["id"];
}

export const InfractionsSection: React.FC<InfractionsSectionProps> = ({
  infractions,
  propertyId,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  // Return condicional DESPUÉS de los cálculos
  if (!infractions.length) {
    return (
      <NoDataSection
        sectionTitle="Infracciones"
        text="No hay infracciones nuevas"
      />
    );
  }

  return (
    <Section title="Infracciones">
      <>
        {infractions.map((infraction) => {
          const severityData = getSeverityData(infraction.severity);
          return (
            <TouchableOpacity
              key={infraction.id}
              onPress={() =>
                navigation.navigate(PROPERTY_SCREENS.INFRACTION_DETAIL, {
                  infraction,
                })
              }
              activeOpacity={0.7}
            >
              <Card
                style={[
                  styles.infractionCard,
                  { borderLeftColor: severityData.color },
                ]}
              >
                <Row align="flex-start">
                  <Col style={styles.infractionInfo}>
                    <Row align="center" style={styles.headerRow}>
                      <Text
                        style={styles.infractionDescription}
                        numberOfLines={2}
                      >
                        {infraction.description}
                      </Text>
                      <View
                        style={[
                          styles.severityChip,
                          {
                            backgroundColor: severityData.color,
                          },
                        ]}
                      >
                        <Text style={styles.severityText}>
                          {severityData.label}
                        </Text>
                      </View>
                    </Row>
                    <Row align="center" style={styles.dateRow}>
                      <MaterialCommunityIcons
                        name="calendar"
                        size={14}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.dateText}>
                        Fecha: {formatDateDMY(infraction.date)}
                      </Text>
                    </Row>
                  </Col>
                  <MaterialCommunityIcons
                    name="chevron-right"
                    size={theme.fontSizes.lg}
                    color={theme.colors.gray500}
                  />
                </Row>
              </Card>
            </TouchableOpacity>
          );
        })}
      </>
    </Section>
  );
};

const styles = StyleSheet.create({
  infractionCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
    borderRadius: theme.radii.md,
    borderLeftWidth: 4,
  },
  severityIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  infractionInfo: {
    flex: 1,
    paddingLeft: theme.spacing.md,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
    alignItems: "flex-start",
  },
  infractionDescription: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
    lineHeight: 20,
  },
  severityChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  severityText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    textTransform: "uppercase",
  },
  dateRow: {
    marginTop: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
});
