import { User } from "./user";
import { ParkingSpot } from "./parking-spot";
import { Reservation } from "./reservation";
import { Rental } from "./rental";
import { Infraction } from "./infraction";
import { Payment } from "./payment";
import { Visit } from "./visit";
import { Pet } from "./pet";
import { Vehicle } from "./vehicle";
import { Tag } from "./tag";
import { Fine } from "./fine";
import { Complaint } from "./complaint";
import { MonthlyMaintenanceCharge } from "./monthly-maintenance-charge";
import { MaintenanceIssueReport } from "./maintenance-issue-report";
import { Facility } from "./facility";
import { Package } from "./package";

export interface PropertyGenerals {
  id: string;
  address: string;
  type: PropertyType;
}

export interface Property {
  id: string;
  address: string;
  type: PropertyType;

  createdAt: string;
  updatedAt: string;

  residents: User[];
  reservations: Reservation[];
  rentals: Rental[];
  infractions: Infraction[];
  payments: Payment[];
  visits: Visit[];
  parkingSpots: ParkingSpot[];
  pets: Pet[];
  vehicles: Vehicle[];
  tags: Tag[];
  fines: Fine[];
  complaints: Complaint[];
  maintenanceIssueReports: MaintenanceIssueReport[];
  monthlyMaintenanceCharges: MonthlyMaintenanceCharge[];
  packages: Package[];
}

export enum PropertyType {
  HOUSE = "HOUSE",
  DEPARTMENT = "DEPARTMENT",
}

export interface PartialProperty {
  id: Property["id"];
  address: Property["address"];
  type: Property["type"];
  _count: {
    parkingSpots: number;
    pets: number;
    residents: number;
    tags: number;
    vehicles: number;
  };
  reservations: Reservation[];
  maintenanceIssueReports: MaintenanceIssueReport[];
  infractions: Infraction[];
  complaints: Complaint[];
  fines: Fine[];
  packages: Package[];
}

export interface PartialReservation {
  id: Reservation["id"];
  status: Reservation["status"];
  startDateTime: Reservation["startDateTime"];
  facility: PartialFacility;
}

interface PartialFacility {
  name: Facility["name"];
}

export interface PartialMaintenanceIssueReport {
  id: MaintenanceIssueReport["id"];
  description: MaintenanceIssueReport["description"];
  status: MaintenanceIssueReport["status"];
  createdAt: MaintenanceIssueReport["createdAt"];
  updatedAt: MaintenanceIssueReport["updatedAt"];
}

export interface PartialComplaint {
  id: Complaint["id"];
  detail: Complaint["detail"];
  priority: Complaint["priority"];
}

export interface PartialInfraction {
  id: Infraction["id"];
  description: Infraction["description"];
  date: Infraction["date"];
  severity: Infraction["severity"];
}

export interface PartialFine {
  id: Fine["id"];
  amount: Fine["amount"];
  isPaid: Fine["isPaid"];
}
