import { StyleSheet, Text, View, ScrollView } from "react-native";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { theme } from "../../theme";
import { InfractionDetailRouteProp } from "../../navigation/types";
import { formatDateDMY } from "../../utils/date-time.utils";
import { getSeverityData } from "../../utils/convertions";
import { DetailTopCard } from "../../components/main/cards/DetailTopCard";
import { DetailCardBase } from "../../components/main/cards/DetailCardBase";

export const InfractionDetailScreen: React.FC = () => {
  const route = useRoute<InfractionDetailRouteProp>();
  const { infraction } = route.params;

  const severityData = getSeverityData(infraction.severity);

  return (
    <GradientView firstLineText="Detalle de Infracción">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header Card */}
        <DetailTopCard
          iconItem={{
            icon: "alert-circle",
            color: severityData.color,
          }}
          label="Infracción"
          tags={[{ label: severityData.label, color: severityData.color }]}
        />

        {/* Infraction Details */}
        <DetailCardBase border>
          <Text style={styles.sectionTitle}>Información de la Infracción</Text>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="alert-circle"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Severidad</Text>
              <Text style={[styles.detailValue, { color: severityData.color }]}>
                {severityData.label}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="text-box"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Descripción</Text>
              <Text style={styles.detailValue}>{infraction.description}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="calendar"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Fecha de la infracción</Text>
              <Text style={styles.detailValue}>
                {formatDateDMY(infraction.date)}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="identifier"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>ID de Infracción</Text>
              <Text style={styles.detailValue}>{infraction.id}</Text>
            </View>
          </View>
        </DetailCardBase>

        {/* Severity Information */}
        <DetailCardBase border>
          <Text style={styles.sectionTitle}>Información de Severidad</Text>

          <View style={styles.severityInfoContainer}>
            <View style={styles.severityHeader}>
              <MaterialCommunityIcons
                name={severityData.icon}
                size={24}
                color={severityData.color}
              />
              <Text
                style={[styles.severityTitle, { color: severityData.color }]}
              >
                {severityData.label}
              </Text>
            </View>

            <Text style={styles.severityDescription}>
              {getSeverityDescription(infraction.severity)}
            </Text>
          </View>
        </DetailCardBase>

        {/* Actions Information */}
        <DetailCardBase border>
          <Text style={styles.sectionTitle}>Información Adicional</Text>

          <View style={styles.infoContainer}>
            <MaterialCommunityIcons
              name="information"
              size={20}
              color={theme.colors.primary}
            />
            <Text style={styles.infoText}>
              Para más información sobre esta infracción o para presentar una
              apelación, contacte a la administración del condominio.
            </Text>
          </View>
        </DetailCardBase>
      </ScrollView>
    </GradientView>
  );
};

const getSeverityDescription = (severity: string): string => {
  switch (severity) {
    case "MINOR":
      return "Infracción menor que requiere atención pero no representa un riesgo significativo.";
    case "MODERATE":
      return "Infracción moderada que puede afectar la convivencia y requiere corrección.";
    case "SEVERE":
      return "Infracción grave que puede resultar en sanciones adicionales o medidas disciplinarias.";
    default:
      return "Nivel de severidad no especificado.";
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
  },
  headerRow: {
    marginBottom: 0,
  },
  severityIndicator: {
    width: 64,
    height: 64,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
  },
  infractionTitle: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  severityChip: {
    alignSelf: "flex-start",
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.md,
  },
  severityText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.white,
  },
  detailCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
  },
  sectionTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.lg,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
  },
  detailContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  detailLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
  },
  severityInfoContainer: {
    backgroundColor: `${theme.colors.gray100}50`,
    borderRadius: theme.radii.md,
    padding: theme.spacing.md,
  },
  severityHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
  },
  severityTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "700",
    marginLeft: theme.spacing.sm,
  },
  severityDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray700,
    lineHeight: 20,
  },
  infoContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    backgroundColor: `${theme.colors.primary}10`,
    borderRadius: theme.radii.md,
    padding: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  infoText: {
    flex: 1,
    fontSize: theme.fontSizes.sm,
    color: theme.colors.primary,
    marginLeft: theme.spacing.sm,
    lineHeight: 20,
  },
});
