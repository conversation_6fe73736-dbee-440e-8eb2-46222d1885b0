import { StyleSheet, Text, View, ScrollView, Image } from "react-native";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/cards/Card";
import { theme } from "../../theme";
import { ComplaintDetailRouteProp } from "../../navigation/types";
import { formatDateDMY } from "../../utils/date-time.utils";
import {
  getProirity,
  getStatusColor,
  getStatusIcon,
  getStatusLabel,
} from "../../utils/convertions";
import { DetailTopCard } from "../../components/main/cards/DetailTopCard";
import { DetailCardBase } from "../../components/main/cards/DetailCardBase";

export const ComplaintDetailScreen: React.FC = () => {
  const route = useRoute<ComplaintDetailRouteProp>();
  const { complaint } = route.params;

  const statusColor = getStatusColor(complaint.status);
  const statusIcon = getStatusIcon(complaint.status);
  const statusLabel = getStatusLabel(complaint.status);
  const priorityLabel = getProirity(complaint.priority);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "HIGH":
        return theme.colors.error;
      case "MEDIUM":
        return theme.colors.warning;
      case "LOW":
        return theme.colors.success;
      default:
        return theme.colors.gray500;
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "HIGH":
        return "alert-circle";
      case "MEDIUM":
        return "alert";
      case "LOW":
        return "information";
      default:
        return "help-circle";
    }
  };

  const priorityColor = getPriorityColor(complaint.priority);
  const priorityIcon = getPriorityIcon(complaint.priority);

  return (
    <GradientView firstLineText="Detalle de Queja">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header Card */}
        <DetailTopCard
          iconItem={{
            icon: "flag-outline",
            color: theme.colors.error,
          }}
          label="Queja"
          tags={[
            { label: statusLabel, color: statusColor },
            { label: priorityLabel, color: priorityColor },
          ]}
        />

        {/* Complaint Details */}
        <DetailCardBase border>
          <Text style={styles.sectionTitle}>Información de la Queja</Text>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="text-box"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Detalle</Text>
              <Text style={styles.detailValue}>{complaint.detail}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name={priorityIcon}
              size={20}
              color={priorityColor}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Prioridad</Text>
              <Text style={[styles.detailValue, { color: priorityColor }]}>
                {priorityLabel}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name={statusIcon}
              size={20}
              color={statusColor}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Estado</Text>
              <Text style={[styles.detailValue, { color: statusColor }]}>
                {statusLabel}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="calendar-plus"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Fecha de creación</Text>
              <Text style={styles.detailValue}>
                {formatDateDMY(complaint.createdAt)}
              </Text>
            </View>
          </View>

          {!!complaint.completedAt && (
            <View style={styles.detailRow}>
              <MaterialCommunityIcons
                name="calendar-check"
                size={20}
                color={theme.colors.success}
              />
              <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Fecha de finalización</Text>
                <Text style={styles.detailValue}>
                  {formatDateDMY(complaint.completedAt)}
                </Text>
              </View>
            </View>
          )}

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="identifier"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>ID de Queja</Text>
              <Text style={styles.detailValue}>{complaint.id}</Text>
            </View>
          </View>
        </DetailCardBase>

        {/* Additional Information */}
        <DetailCardBase border>
          <Text style={styles.sectionTitle}>Información Adicional</Text>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="home"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Tipo de queja</Text>
              <Text style={styles.detailValue}>
                {complaint.complaintType.name}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="account"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Descripción de tipo</Text>
              <Text style={styles.detailValue}>
                {complaint.complaintType.name}
              </Text>
            </View>
          </View>
        </DetailCardBase>

        {/* Images */}
        {complaint.images && complaint.images.length > 0 && (
          <Card style={styles.detailCard}>
            <Text style={styles.sectionTitle}>Imágenes del Reporte</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.imagesContainer}
            >
              {complaint.images.map((image, index) => (
                <View key={image.id ?? index} style={styles.imageWrapper}>
                  <Image
                    source={{ uri: image.path }}
                    style={styles.reportImage}
                    resizeMode="cover"
                  />
                </View>
              ))}
            </ScrollView>
          </Card>
        )}

        {/* Status Information */}
        {complaint.status !== "RESOLVED" && (
          <Card style={styles.infoCard} withoutShadow>
            <View style={styles.infoContainer}>
              <MaterialCommunityIcons
                name="information"
                size={20}
                color={theme.colors.primary}
              />
              <Text style={styles.infoText}>
                Su queja está siendo procesada. Recibirá actualizaciones sobre
                el progreso y la resolución de su solicitud.
              </Text>
            </View>
          </Card>
        )}

        {complaint.status === "RESOLVED" && (
          <Card style={styles.successCard} withoutShadow>
            <View style={styles.successContainer}>
              <MaterialCommunityIcons
                name="check-circle"
                size={24}
                color={theme.colors.success}
              />
              <View style={styles.successContent}>
                <Text style={styles.successTitle}>Queja Resuelta</Text>
                <Text style={styles.successText}>
                  Su queja ha sido resuelta exitosamente. Si tiene alguna
                  pregunta adicional, no dude en contactar a la administración.
                </Text>
              </View>
            </View>
          </Card>
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
    backgroundColor: theme.colors.white,
  },
  headerRow: {
    marginBottom: 0,
  },
  statusIndicator: {
    width: 64,
    height: 64,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
  },
  complaintTitle: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  chipRow: {
    gap: theme.spacing.sm,
  },
  statusChip: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.md,
    marginRight: theme.spacing.sm,
  },
  priorityChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.md,
    gap: 4,
  },
  chipText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.white,
  },
  detailCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
    backgroundColor: theme.colors.white,
    borderColor: theme.colors.primary,
    borderWidth: 1,
    borderRadius: theme.radii.lg,
  },
  sectionTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.lg,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
  },
  detailContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  detailLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    lineHeight: 20,
  },
  infoCard: {
    marginBottom: theme.spacing.md,
    backgroundColor: `${theme.colors.primary}05`,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  infoContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: theme.spacing.md,
  },
  infoText: {
    flex: 1,
    fontSize: theme.fontSizes.sm,
    color: theme.colors.primary,
    marginLeft: theme.spacing.sm,
    lineHeight: 20,
  },
  successCard: {
    marginBottom: theme.spacing.md,
    backgroundColor: `${theme.colors.success}05`,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.success,
  },
  successContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: theme.spacing.md,
  },
  successContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  successTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "700",
    color: theme.colors.success,
    marginBottom: theme.spacing.xs,
  },
  successText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.success,
    lineHeight: 20,
  },
  imagesContainer: {
    marginTop: theme.spacing.sm,
  },
  imageWrapper: {
    marginRight: theme.spacing.md,
  },
  reportImage: {
    width: 120,
    height: 120,
    borderRadius: theme.radii.md,
    backgroundColor: theme.colors.gray100,
  },
});
