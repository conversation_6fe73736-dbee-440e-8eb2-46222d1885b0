import { StyleSheet, Text, View } from "react-native";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { theme } from "../../theme";
import { ReservationDetailRouteProp } from "../../navigation/types";
import {
  formatDateDMY,
  getStringTimeFromIso,
} from "../../utils/date-time.utils";
import { getReservationStatusData } from "../../utils/convertions";
import { DetailTopCard } from "../../components/main/cards/DetailTopCard";
import { DetailCardBase } from "../../components/main/cards/DetailCardBase";

export const ReservationDetailScreen: React.FC = () => {
  const route = useRoute<ReservationDetailRouteProp>();
  const { reservation } = route.params;

  const statusData = getReservationStatusData(reservation.status);

  const formatDateTime = (dateTime: string) => {
    return `${formatDateDMY(dateTime)} ${getStringTimeFromIso(dateTime)}`;
  };

  return (
    <GradientView firstLineText="Detalle de Reservación">
      {/* Header Card */}
      <DetailTopCard
        iconItem={{
          icon: "calendar-check",
          color: statusData.color,
        }}
        label="Reservación"
        tags={[{ label: statusData.label, color: statusData.color }]}
      />

      {/* Reservation Details */}
      <DetailCardBase border>
        <Text style={styles.sectionTitle}>Información de la Reservación</Text>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name="calendar-start"
            size={20}
            color={theme.colors.primary}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Fecha y hora de inicio</Text>
            <Text style={styles.detailValue}>
              {formatDateTime(reservation.startDateTime)}
            </Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name="calendar-end"
            size={20}
            color={theme.colors.primary}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Fecha y hora de fin</Text>
            <Text style={styles.detailValue}>
              {formatDateTime(reservation.endDateTime)}
            </Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name="account-group"
            size={20}
            color={theme.colors.primary}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Cantidad de personas</Text>
            <Text style={styles.detailValue}>{reservation.amountOfPeople}</Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name="identifier"
            size={20}
            color={theme.colors.primary}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>ID de Reservación</Text>
            <Text style={styles.detailValue}>{reservation.id}</Text>
          </View>
        </View>
      </DetailCardBase>

      {/* Status Information */}
      <DetailCardBase border>
        <Text style={styles.sectionTitle}>Infromación adicional</Text>

        <View style={styles.detailRow}>
          <MaterialCommunityIcons
            name="calendar-plus"
            size={20}
            color={theme.colors.gray500}
          />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Fecha de creación</Text>
            <Text style={styles.detailValue}>
              {formatDateDMY(reservation.createdAt)}
            </Text>
          </View>
        </View>

        {reservation.authorizedAt && (
          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="check-circle"
              size={20}
              color={theme.colors.success}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Fecha de autorización</Text>
              <Text style={styles.detailValue}>
                {formatDateDMY(reservation.authorizedAt)}
              </Text>
            </View>
          </View>
        )}

        {reservation.deniedAt && (
          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="close-circle"
              size={20}
              color={theme.colors.error}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Fecha de rechazo</Text>
              <Text style={styles.detailValue}>
                {formatDateDMY(reservation.deniedAt)}
              </Text>
            </View>
          </View>
        )}

        {reservation.deniedReason && (
          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="message-text"
              size={20}
              color={theme.colors.error}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Motivo de rechazo</Text>
              <Text style={styles.detailValue}>{reservation.deniedReason}</Text>
            </View>
          </View>
        )}
      </DetailCardBase>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
  },
  headerRow: {
    marginBottom: 0,
  },
  statusIndicator: {
    width: 64,
    height: 64,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
  },
  facilityName: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  statusChip: {
    alignSelf: "flex-start",
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.md,
  },
  statusText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.white,
  },
  detailCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
  },
  sectionTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.lg,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
  },
  detailContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  detailLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
  },
});
