import { useState, useMemo } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/cards/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { PackageStatus } from "../../interfaces/package";
import { formatDateDMY } from "../../utils/date-time.utils";
import { Loading } from "../../components/Loading";
import {
  PropertyPackagesRouteProp,
  PropertyStackNavigationProp,
} from "../../navigation/types";
import { useRoute, useNavigation } from "@react-navigation/native";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { NoDataText } from "../../components/NoDataText";

const getStatusColor = (status: PackageStatus) => {
  switch (status) {
    case PackageStatus.PENDING:
      return theme.colors.warning;
    case PackageStatus.DELIVERED:
      return theme.colors.success;
    case PackageStatus.EXPIRED:
      return theme.colors.error;
    default:
      return theme.colors.text;
  }
};

const getStatusLabel = (status: PackageStatus) => {
  switch (status) {
    case PackageStatus.PENDING:
      return "Pendiente";
    case PackageStatus.DELIVERED:
      return "Entregado";
    case PackageStatus.EXPIRED:
      return "Expirado";
    default:
      return status;
  }
};

const getStatusIcon = (status: PackageStatus) => {
  switch (status) {
    case PackageStatus.PENDING:
      return "clock-outline";
    case PackageStatus.DELIVERED:
      return "check-circle";
    case PackageStatus.EXPIRED:
      return "close-circle";
    default:
      return "package-variant";
  }
};

export const PropertyPackagesScreen: React.FC = () => {
  const route = useRoute<PropertyPackagesRouteProp>();
  const navigation = useNavigation<PropertyStackNavigationProp>();
  const { packages, property } = route.params || {};

  const [selectedFilter, setSelectedFilter] = useState<PackageStatus | "ALL">(
    "ALL"
  );

  const { data: userData, isLoading } = useCachedQuery<Me>(`mobile/me`);

  const filteredPackages = useMemo(() => {
    if (selectedFilter === "ALL") return packages;
    return packages.filter((pkg) => pkg.status === selectedFilter);
  }, [packages, selectedFilter]);

  const statusCounts = useMemo(() => {
    return {
      all: packages.length,
      pending: packages.filter((pkg) => pkg.status === PackageStatus.PENDING)
        .length,
      delivered: packages.filter(
        (pkg) => pkg.status === PackageStatus.DELIVERED
      ).length,
      expired: packages.filter((pkg) => pkg.status === PackageStatus.EXPIRED)
        .length,
    };
  }, [packages]);

  if (isLoading) return <Loading />;

  return (
    <GradientView firstLineText="Paquetes">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Filter Buttons */}
        <View style={styles.filterContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[
                styles.filterButton,
                selectedFilter === "ALL" && styles.filterButtonActive,
              ]}
              onPress={() => setSelectedFilter("ALL")}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  selectedFilter === "ALL" && styles.filterButtonTextActive,
                ]}
              >
                Todos ({statusCounts.all})
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                selectedFilter === PackageStatus.PENDING &&
                  styles.filterButtonActive,
              ]}
              onPress={() => setSelectedFilter(PackageStatus.PENDING)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  selectedFilter === PackageStatus.PENDING &&
                    styles.filterButtonTextActive,
                ]}
              >
                Pendientes ({statusCounts.pending})
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                selectedFilter === PackageStatus.DELIVERED &&
                  styles.filterButtonActive,
              ]}
              onPress={() => setSelectedFilter(PackageStatus.DELIVERED)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  selectedFilter === PackageStatus.DELIVERED &&
                    styles.filterButtonTextActive,
                ]}
              >
                Entregados ({statusCounts.delivered})
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                selectedFilter === PackageStatus.EXPIRED &&
                  styles.filterButtonActive,
              ]}
              onPress={() => setSelectedFilter(PackageStatus.EXPIRED)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  selectedFilter === PackageStatus.EXPIRED &&
                    styles.filterButtonTextActive,
                ]}
              >
                Expirados ({statusCounts.expired})
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Packages List */}
        {filteredPackages.length === 0 ? (
          <NoDataText text="No hay paquetes para mostrar" />
        ) : (
          filteredPackages.map((pkg) => (
            <TouchableOpacity
              key={pkg.id}
              onPress={() =>
                navigation.navigate(PROPERTY_SCREENS.PACKAGE_DETAIL, {
                  package: pkg,
                })
              }
            >
              <Card style={styles.packageCard}>
                <Row style={styles.headerRow}>
                  <View style={styles.packageIcon}>
                    <MaterialCommunityIcons
                      name="package-variant"
                      size={24}
                      color={theme.colors.primary}
                    />
                  </View>
                  <Col style={styles.packageInfo}>
                    <Row style={styles.titleRow}>
                      <Text style={styles.packageNumber} numberOfLines={1}>
                        Paquete #{pkg.number}
                      </Text>
                      <View
                        style={[
                          styles.statusChip,
                          {
                            backgroundColor: getStatusColor(pkg.status) + "20",
                          },
                        ]}
                      >
                        <MaterialCommunityIcons
                          name={getStatusIcon(pkg.status)}
                          size={12}
                          color={getStatusColor(pkg.status)}
                        />
                        <Text
                          style={[
                            styles.statusText,
                            { color: getStatusColor(pkg.status) },
                          ]}
                        >
                          {getStatusLabel(pkg.status)}
                        </Text>
                      </View>
                    </Row>
                    {pkg.notes && (
                      <Text style={styles.notes} numberOfLines={2}>
                        {pkg.notes}
                      </Text>
                    )}
                    <Row style={styles.footerRow}>
                      <Text style={styles.dateText}>
                        Recibido: {formatDateDMY(pkg.receivedAt)}
                      </Text>
                      {pkg.deliveredTo &&
                        pkg.status === PackageStatus.DELIVERED && (
                          <Text
                            style={styles.deliveredToText}
                            numberOfLines={1}
                          >
                            Entregado a: {pkg.deliveredTo}
                          </Text>
                        )}
                    </Row>
                  </Col>
                </Row>
              </Card>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: theme.spacing.md,
  },
  filterContainer: {
    marginBottom: theme.spacing.lg,
  },
  filterButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    marginRight: theme.spacing.sm,
    borderRadius: theme.radii.md,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: "500",
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  packageCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  headerRow: {
    alignItems: "flex-start",
  },
  packageIcon: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    backgroundColor: theme.colors.primary + "15",
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  packageInfo: {
    flex: 1,
  },
  titleRow: {
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: theme.spacing.xs,
  },
  packageNumber: {
    fontSize: 16,
    fontWeight: "600",
    color: theme.colors.text,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
    marginLeft: 4,
  },
  notes: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    lineHeight: 20,
  },
  footerRow: {
    justifyContent: "space-between",
    alignItems: "center",
  },
  dateText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  deliveredToText: {
    fontSize: 12,
    color: theme.colors.success,
    fontWeight: "500",
    flex: 1,
    textAlign: "right",
  },
});
